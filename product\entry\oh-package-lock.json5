{"meta": {"stableOrder": true}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"basic@../../commons/basic": "basic@../../commons/basic", "createmeeting@../../features/createmeeting": "createmeeting@../../features/createmeeting", "home@../../features/home": "home@../../features/home", "hsmeeting_hmos_sdk@../../features/library": "hsmeeting_hmos_sdk@../../features/library", "libcapture.so@../../features/library/src/main/cpp/types/capture": "libcapture.so@../../features/library/src/main/cpp/types/capture", "libdesktopshare.so@../../features/library/src/main/cpp/types/desktopshare": "libdesktopshare.so@../../features/library/src/main/cpp/types/desktopshare", "libencoder.so@../../features/library/src/main/cpp/types/encoder": "libencoder.so@../../features/library/src/main/cpp/types/encoder", "libentry.so@../../features/library/src/main/cpp/types/libentry": "libentry.so@../../features/library/src/main/cpp/types/libentry", "libplayer.so@../../features/library/src/main/cpp/types/decoder": "libplayer.so@../../features/library/src/main/cpp/types/decoder", "routermoudel@../../commons/RouterMoudel": "routermoudel@../../commons/RouterMoudel", "schedulemeeting@../../features/schedulemeeting": "schedulemeeting@../../features/schedulemeeting", "settings@../../features/settings": "settings@../../features/settings"}, "packages": {"basic@../../commons/basic": {"name": "basic", "version": "1.0.0", "resolved": "../../commons/basic", "registryType": "local", "packageType": "InterfaceHar"}, "createmeeting@../../features/createmeeting": {"name": "createmeeting", "version": "1.0.0", "resolved": "../../features/createmeeting", "registryType": "local", "dependencies": {"hsmeeting_hmos_sdk": "file:../library", "basic": "file:../../commons/basic", "routermoudel": "file:../../commons/RouterMoudel"}, "packageType": "InterfaceHar"}, "home@../../features/home": {"name": "home", "version": "1.0.0", "resolved": "../../features/home", "registryType": "local", "dependencies": {"basic": "file:../../commons/basic", "routermoudel": "file:../../commons/RouterMoudel", "hsmeeting_hmos_sdk": "file:../library"}, "packageType": "InterfaceHar"}, "hsmeeting_hmos_sdk@../../features/library": {"name": "hsmeeting_hmos_sdk", "version": "1.0.0", "resolved": "../../features/library", "registryType": "local", "dependencies": {"libentry.so": "file:./src/main/cpp/types/libentry", "libencoder.so": "file:./src/main/cpp/types/encoder", "libplayer.so": "file:./src/main/cpp/types/decoder", "libdesktopshare.so": "file:./src/main/cpp/types/desktopshare", "libcapture.so": "file:./src/main/cpp/types/capture", "basic": "file:../../commons/basic"}}, "libcapture.so@../../features/library/src/main/cpp/types/capture": {"name": "libcapture.so", "version": "1.0.0", "resolved": "../../features/library/src/main/cpp/types/capture", "registryType": "local"}, "libdesktopshare.so@../../features/library/src/main/cpp/types/desktopshare": {"name": "libdesktopshare.so", "version": "1.0.0", "resolved": "../../features/library/src/main/cpp/types/desktopshare", "registryType": "local"}, "libencoder.so@../../features/library/src/main/cpp/types/encoder": {"name": "libencoder.so", "version": "1.0.0", "resolved": "../../features/library/src/main/cpp/types/encoder", "registryType": "local"}, "libentry.so@../../features/library/src/main/cpp/types/libentry": {"name": "libentry.so", "version": "1.0.0", "resolved": "../../features/library/src/main/cpp/types/libentry", "registryType": "local"}, "libplayer.so@../../features/library/src/main/cpp/types/decoder": {"name": "libplayer.so", "version": "1.0.0", "resolved": "../../features/library/src/main/cpp/types/decoder", "registryType": "local"}, "routermoudel@../../commons/RouterMoudel": {"name": "routermoudel", "version": "1.0.0", "resolved": "../../commons/RouterMoudel", "registryType": "local"}, "schedulemeeting@../../features/schedulemeeting": {"name": "schedulemeeting", "version": "1.0.0", "resolved": "../../features/schedulemeeting", "registryType": "local", "dependencies": {"hsmeeting_hmos_sdk": "file:../library", "basic": "file:../../commons/basic", "routermoudel": "file:../../commons/RouterMoudel"}, "packageType": "InterfaceHar"}, "settings@../../features/settings": {"name": "settings", "version": "1.0.0", "resolved": "../../features/settings", "registryType": "local", "dependencies": {"hsmeeting_hmos_sdk": "file:../library", "basic": "file:../../commons/basic", "routermoudel": "file:../../commons/RouterMoudel"}, "packageType": "InterfaceHar"}}}